import AsyncStorage from '@react-native-async-storage/async-storage';

export interface WeightGoal {
  startWeight: number; // kg
  targetWeight: number; // kg
  goalWeightLoss: number; // kg to lose
  targetDate: string; // ISO date string
  startDate: string; // ISO date string
  weeklyGoal: number; // kg per week
  dailyCalorieDeficit: number; // calories per day needed
  createdAt: number; // timestamp
}

export interface DailyProgress {
  date: string; // YYYY-MM-DD format
  caloriesConsumed: number;
  caloriesFromScanner: number;
  caloriesFromWeeklyPlan: number;
  caloriesFromManualLog: number;
  // ADD PROTEIN TRACKING
  proteinConsumed: number;
  proteinFromScanner: number;
  proteinFromWeeklyPlan: number;
  proteinFromManualLog: number;
  estimatedWeightChange: number; // kg (negative for loss)
  mealsLogged: number;
  scannedMeals: number;
  weeklyPlanMeals: number;
  createdAt: number;
  updatedAt: number;
}

export interface WeightProgress {
  currentEstimatedWeight: number;
  totalWeightLost: number;
  remainingWeightToLose: number;
  progressPercentage: number;
  daysElapsed: number;
  daysRemaining: number;
  averageDailyProgress: number;
  isOnTrack: boolean;
  projectedCompletionDate: string;
  milestones: {
    quarter: boolean; // 25%
    half: boolean; // 50%
    threeQuarter: boolean; // 75%
    complete: boolean; // 100%
  };
  motivationalMessage: string;
  weeklyTrend: 'improving' | 'stable' | 'declining';
}

class WeightGoalTracker {
  private static instance: WeightGoalTracker;
  private readonly WEIGHT_GOAL_KEY = 'weight_goal_data';
  private readonly DAILY_PROGRESS_KEY = 'daily_progress_data';
  private readonly CALORIES_PER_KG = 7700; // Approximate calories in 1kg of body fat

  static getInstance(): WeightGoalTracker {
    if (!WeightGoalTracker.instance) {
      WeightGoalTracker.instance = new WeightGoalTracker();
    }
    return WeightGoalTracker.instance;
  }

  // Initialize weight goal from onboarding data with validation
  async initializeWeightGoal(onboardingData: {
    currentWeight: number;
    targetWeight: number;
    timeframe: number; // weeks
    activityLevel: string;
  }): Promise<WeightGoal> {
    try {
      // Validate input data
      if (!onboardingData.currentWeight || onboardingData.currentWeight <= 0) {
        throw new Error('Invalid current weight');
      }
      if (!onboardingData.targetWeight || onboardingData.targetWeight <= 0) {
        throw new Error('Invalid target weight');
      }
      if (!onboardingData.timeframe || onboardingData.timeframe <= 0 || onboardingData.timeframe > 104) {
        throw new Error('Invalid timeframe (must be 1-104 weeks)');
      }

      const startDate = new Date();
      const targetDate = new Date();
      targetDate.setDate(startDate.getDate() + (onboardingData.timeframe * 7));

      const goalWeightLoss = Math.abs(onboardingData.currentWeight - onboardingData.targetWeight);
      const weeklyGoal = goalWeightLoss / onboardingData.timeframe;
      const dailyCalorieDeficit = (weeklyGoal * this.CALORIES_PER_KG) / 7;

      const weightGoal: WeightGoal = {
        startWeight: onboardingData.currentWeight,
        targetWeight: onboardingData.targetWeight,
        goalWeightLoss,
        targetDate: targetDate.toISOString(),
        startDate: startDate.toISOString(),
        weeklyGoal,
        dailyCalorieDeficit,
        createdAt: Date.now()
      };

      // Validate the created weight goal before storing
      if (!this.isValidWeightGoal(weightGoal)) {
        throw new Error('Generated weight goal failed validation');
      }

      // Store with retry mechanism
      let retries = 3;
      while (retries > 0) {
        try {
          await AsyncStorage.setItem(this.WEIGHT_GOAL_KEY, JSON.stringify(weightGoal));
          break;
        } catch (storageError) {
          retries--;
          if (retries === 0) throw storageError;
          console.warn(`⚠️ Storage retry ${3 - retries}/3 for weight goal`);
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      console.log('✅ Weight goal initialized and validated:', weightGoal);

      return weightGoal;
    } catch (error) {
      console.error('❌ Error initializing weight goal:', error);
      throw error;
    }
  }

  // Get current weight goal with validation
  async getWeightGoal(): Promise<WeightGoal | null> {
    try {
      const goalJson = await AsyncStorage.getItem(this.WEIGHT_GOAL_KEY);
      if (!goalJson) {
        console.log('📊 No weight goal found in AsyncStorage');
        return null;
      }

      const weightGoal = JSON.parse(goalJson);

      // Validate weight goal structure
      if (!this.isValidWeightGoal(weightGoal)) {
        console.error('❌ Invalid weight goal structure, clearing corrupted data');
        await AsyncStorage.removeItem(this.WEIGHT_GOAL_KEY);
        return null;
      }

      return weightGoal;
    } catch (error) {
      console.error('❌ Error getting weight goal:', error);
      // Clear corrupted data
      try {
        await AsyncStorage.removeItem(this.WEIGHT_GOAL_KEY);
      } catch (clearError) {
        console.error('❌ Error clearing corrupted weight goal:', clearError);
      }
      return null;
    }
  }

  // Validate weight goal structure
  private isValidWeightGoal(goal: any): goal is WeightGoal {
    return (
      goal &&
      typeof goal.startWeight === 'number' &&
      typeof goal.targetWeight === 'number' &&
      typeof goal.goalWeightLoss === 'number' &&
      typeof goal.targetDate === 'string' &&
      typeof goal.startDate === 'string' &&
      typeof goal.weeklyGoal === 'number' &&
      typeof goal.dailyCalorieDeficit === 'number' &&
      typeof goal.createdAt === 'number' &&
      goal.startWeight > 0 &&
      goal.targetWeight > 0 &&
      goal.goalWeightLoss >= 0
    );
  }

  // Log calories AND PROTEIN from different sources
  async logDailyNutrition(
    date: string, // YYYY-MM-DD
    calories: number,
    protein: number = 0, // ADD PROTEIN PARAMETER
    source: 'scanner' | 'weekly_plan' | 'manual'
  ): Promise<void> {
    try {
      const progressData = await this.getDailyProgress(date) || this.createEmptyDailyProgress(date);

      // Update calories AND PROTEIN based on source
      switch (source) {
        case 'scanner':
          progressData.caloriesFromScanner += calories;
          progressData.proteinFromScanner += protein;
          progressData.scannedMeals += 1;
          break;
        case 'weekly_plan':
          progressData.caloriesFromWeeklyPlan += calories;
          progressData.proteinFromWeeklyPlan += protein;
          progressData.weeklyPlanMeals += 1;
          break;
        case 'manual':
          progressData.caloriesFromManualLog += calories;
          progressData.proteinFromManualLog += protein;
          break;
      }

      // Update totals
      progressData.caloriesConsumed =
        progressData.caloriesFromScanner +
        progressData.caloriesFromWeeklyPlan +
        progressData.caloriesFromManualLog;

      progressData.proteinConsumed =
        progressData.proteinFromScanner +
        progressData.proteinFromWeeklyPlan +
        progressData.proteinFromManualLog;

      progressData.mealsLogged =
        progressData.scannedMeals +
        progressData.weeklyPlanMeals;

      // Calculate realistic estimated weight change based on accumulated deficit over time
      const weightGoal = await this.getWeightGoal();
      if (weightGoal) {
        // Get accumulated progress over the last 7 days to calculate realistic weight change
        const weeklyProgress = await this.getWeeklyAccumulatedProgress(date);
        progressData.estimatedWeightChange = this.calculateRealisticWeightChange(
          weeklyProgress,
          weightGoal,
          progressData.caloriesConsumed
        );
      }

      progressData.updatedAt = Date.now();

      // Store updated progress
      await this.storeDailyProgress(progressData);

      console.log(`📊 Logged ${calories} calories and ${protein}g protein from ${source} for ${date}`);
    } catch (error) {
      console.error('❌ Error logging daily nutrition:', error);
      throw error;
    }
  }

  // LEGACY METHOD - Keep for backward compatibility
  async logDailyCalories(
    date: string,
    calories: number,
    source: 'scanner' | 'weekly_plan' | 'manual'
  ): Promise<void> {
    return this.logDailyNutrition(date, calories, 0, source);
  }

  // Get daily progress for a specific date with validation and timeout
  async getDailyProgress(date: string): Promise<DailyProgress | null> {
    try {
      if (!this.isValidDateString(date)) {
        console.error('❌ Invalid date format:', date);
        return null;
      }

      // Add timeout for AsyncStorage operations
      const timeoutPromise = new Promise<null>((_, reject) =>
        setTimeout(() => reject(new Error('getDailyProgress timeout')), 2000)
      );

      const getProgressPromise = async (): Promise<DailyProgress | null> => {
        const allProgressJson = await AsyncStorage.getItem(this.DAILY_PROGRESS_KEY);
        if (!allProgressJson) {
          console.log('📊 No daily progress found in AsyncStorage');
          return null;
        }

        const allProgress: { [date: string]: DailyProgress } = JSON.parse(allProgressJson);

        // Validate progress data structure
        if (!this.isValidProgressData(allProgress)) {
          console.error('❌ Invalid progress data structure, clearing corrupted data');
          await AsyncStorage.removeItem(this.DAILY_PROGRESS_KEY);
          return null;
        }

        return allProgress[date] || null;
      };

      return await Promise.race([getProgressPromise(), timeoutPromise]);
    } catch (error) {
      console.error('❌ Error getting daily progress (with timeout):', error);
      // Clear corrupted data with timeout protection
      try {
        const clearPromise = AsyncStorage.removeItem(this.DAILY_PROGRESS_KEY);
        const clearTimeout = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Clear timeout')), 1000)
        );
        await Promise.race([clearPromise, clearTimeout]);
      } catch (clearError) {
        console.error('❌ Error clearing corrupted progress data (timeout):', clearError);
      }
      return null;
    }
  }

  // Validate date string format (YYYY-MM-DD)
  private isValidDateString(date: string): boolean {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    return dateRegex.test(date) && !isNaN(Date.parse(date));
  }

  // Validate progress data structure
  private isValidProgressData(data: any): boolean {
    if (!data || typeof data !== 'object') return false;

    for (const [date, progress] of Object.entries(data)) {
      if (!this.isValidDateString(date) || !this.isValidDailyProgress(progress)) {
        return false;
      }
    }
    return true;
  }

  // Validate daily progress structure
  private isValidDailyProgress(progress: any): progress is DailyProgress {
    return (
      progress &&
      typeof progress.date === 'string' &&
      typeof progress.caloriesConsumed === 'number' &&
      typeof progress.caloriesFromScanner === 'number' &&
      typeof progress.caloriesFromWeeklyPlan === 'number' &&
      typeof progress.caloriesFromManualLog === 'number' &&
      typeof progress.estimatedWeightChange === 'number' &&
      typeof progress.mealsLogged === 'number' &&
      typeof progress.scannedMeals === 'number' &&
      typeof progress.weeklyPlanMeals === 'number' &&
      typeof progress.createdAt === 'number' &&
      typeof progress.updatedAt === 'number' &&
      progress.caloriesConsumed >= 0 &&
      progress.mealsLogged >= 0
    );
  }

  // Create empty daily progress entry - UPDATED WITH PROTEIN
  private createEmptyDailyProgress(date: string): DailyProgress {
    return {
      date,
      caloriesConsumed: 0,
      caloriesFromScanner: 0,
      caloriesFromWeeklyPlan: 0,
      caloriesFromManualLog: 0,
      proteinConsumed: 0,
      proteinFromScanner: 0,
      proteinFromWeeklyPlan: 0,
      proteinFromManualLog: 0,
      estimatedWeightChange: 0,
      mealsLogged: 0,
      scannedMeals: 0,
      weeklyPlanMeals: 0,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
  }

  // Store daily progress with validation and error recovery
  private async storeDailyProgress(progress: DailyProgress): Promise<void> {
    try {
      // Validate progress data before storing
      if (!this.isValidDailyProgress(progress)) {
        throw new Error('Invalid daily progress data structure');
      }

      const allProgressJson = await AsyncStorage.getItem(this.DAILY_PROGRESS_KEY);
      let allProgress: { [date: string]: DailyProgress } = {};

      if (allProgressJson) {
        try {
          allProgress = JSON.parse(allProgressJson);
          // Validate existing data
          if (!this.isValidProgressData(allProgress)) {
            console.warn('⚠️ Existing progress data is corrupted, starting fresh');
            allProgress = {};
          }
        } catch (parseError) {
          console.warn('⚠️ Failed to parse existing progress data, starting fresh');
          allProgress = {};
        }
      }

      allProgress[progress.date] = progress;

      // Keep only last 90 days of data for performance
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 90);
      const cutoffDateStr = cutoffDate.toISOString().split('T')[0];

      const filteredProgress: { [date: string]: DailyProgress } = {};
      Object.entries(allProgress).forEach(([date, data]) => {
        if (date >= cutoffDateStr && this.isValidDailyProgress(data)) {
          filteredProgress[date] = data;
        }
      });

      // Atomic write with backup
      const dataToStore = JSON.stringify(filteredProgress);
      await AsyncStorage.setItem(this.DAILY_PROGRESS_KEY, dataToStore);

      console.log(`✅ Daily progress stored successfully for ${progress.date}`);
    } catch (error) {
      console.error('❌ Error storing daily progress:', error);

      // Try to recover by creating a minimal valid entry
      try {
        const minimalProgress = this.createEmptyDailyProgress(progress.date);
        minimalProgress.caloriesConsumed = progress.caloriesConsumed || 0;
        minimalProgress.estimatedWeightChange = progress.estimatedWeightChange || 0;

        const recoveryData = { [progress.date]: minimalProgress };
        await AsyncStorage.setItem(this.DAILY_PROGRESS_KEY, JSON.stringify(recoveryData));
        console.log('🔄 Created recovery progress entry');
      } catch (recoveryError) {
        console.error('❌ Failed to create recovery entry:', recoveryError);
      }

      throw error;
    }
  }

  // Calculate current weight progress
  async calculateWeightProgress(): Promise<WeightProgress | null> {
    try {
      const weightGoal = await this.getWeightGoal();
      if (!weightGoal) {
        console.log('⚠️ No weight goal found - user may not have set weight loss goals');
        return null;
      }

      const startDate = new Date(weightGoal.startDate);
      const targetDate = new Date(weightGoal.targetDate);
      const now = new Date();

      const daysElapsed = Math.max(0, Math.floor((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)));
      const totalDays = Math.floor((targetDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const daysRemaining = Math.max(0, totalDays - daysElapsed);

      // Calculate total weight lost from daily progress
      const allProgressJson = await AsyncStorage.getItem(this.DAILY_PROGRESS_KEY);
      let totalWeightLost = 0;
      let recentProgress: DailyProgress[] = [];

      if (allProgressJson) {
        const allProgress: { [date: string]: DailyProgress } = JSON.parse(allProgressJson);
        totalWeightLost = Object.values(allProgress)
          .reduce((sum, day) => sum + Math.abs(Math.min(0, day.estimatedWeightChange)), 0);

        // Get last 7 days for trend analysis
        const last7Days = Object.entries(allProgress)
          .sort(([a], [b]) => b.localeCompare(a))
          .slice(0, 7)
          .map(([, progress]) => progress);
        recentProgress = last7Days;
      }

      const currentEstimatedWeight = Math.max(0, weightGoal.startWeight - totalWeightLost);
      const remainingWeightToLose = Math.max(0, currentEstimatedWeight - weightGoal.targetWeight);
      const progressPercentage = Math.min(100, Math.max(0, (totalWeightLost / Math.max(0.1, weightGoal.goalWeightLoss)) * 100));

      const averageDailyProgress = daysElapsed > 0 ? totalWeightLost / daysElapsed : 0;
      const requiredDailyProgress = daysRemaining > 0 ? remainingWeightToLose / daysRemaining : 0;
      const isOnTrack = averageDailyProgress >= requiredDailyProgress * 0.8; // 80% tolerance

      // Calculate milestones
      const milestones = {
        quarter: progressPercentage >= 25,
        half: progressPercentage >= 50,
        threeQuarter: progressPercentage >= 75,
        complete: progressPercentage >= 100,
      };

      // Generate motivational message
      const motivationalMessage = this.getMotivationalMessage(progressPercentage, isOnTrack, daysElapsed);

      // Calculate weekly trend
      const weeklyTrend = this.calculateWeeklyTrend(recentProgress);

      // Project completion date
      let projectedCompletionDate = targetDate.toISOString();
      if (averageDailyProgress > 0 && remainingWeightToLose > 0) {
        const projectedDaysRemaining = remainingWeightToLose / averageDailyProgress;
        const projectedDate = new Date();
        projectedDate.setDate(projectedDate.getDate() + projectedDaysRemaining);
        projectedCompletionDate = projectedDate.toISOString();
      }

      return {
        currentEstimatedWeight,
        totalWeightLost,
        remainingWeightToLose,
        progressPercentage,
        daysElapsed,
        daysRemaining,
        averageDailyProgress,
        isOnTrack,
        projectedCompletionDate,
        milestones,
        motivationalMessage,
        weeklyTrend,
      };
    } catch (error) {
      console.error('❌ Error calculating weight progress:', error);
      return null;
    }
  }

  // Get progress for last N days
  async getRecentProgress(days: number = 7): Promise<DailyProgress[]> {
    try {
      const allProgressJson = await AsyncStorage.getItem(this.DAILY_PROGRESS_KEY);
      if (!allProgressJson) return [];

      const allProgress: { [date: string]: DailyProgress } = JSON.parse(allProgressJson);

      const recentDates: string[] = [];
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        recentDates.push(date.toISOString().split('T')[0]);
      }

      return recentDates.map(date =>
        allProgress[date] || this.createEmptyDailyProgress(date)
      );
    } catch (error) {
      console.error('❌ Error getting recent progress:', error);
      return [];
    }
  }

  // Generate motivational message based on progress
  private getMotivationalMessage(progressPercentage: number, isOnTrack: boolean, daysElapsed: number): string {
    if (progressPercentage >= 100) {
      return "🎉 Congratulations! You've reached your weight goal!";
    }

    if (progressPercentage >= 75) {
      return isOnTrack ? "💪 Almost there! You're crushing your goals!" : "🔥 So close! Let's push through together!";
    }

    if (progressPercentage >= 50) {
      return isOnTrack ? "⭐ Halfway there! Your dedication is paying off!" : "📈 Great progress! Stay focused on your goal!";
    }

    if (progressPercentage >= 25) {
      return isOnTrack ? "🎯 Quarter way done! You're building great habits!" : "💯 Good start! Consistency is key!";
    }

    if (daysElapsed < 7) {
      return "🌟 Welcome to your weight loss journey! Every step counts!";
    }

    return isOnTrack ? "🚀 You're on the right track! Keep going!" : "💪 Let's refocus and get back on track!";
  }

  // Get accumulated progress over the last 7 days
  private async getWeeklyAccumulatedProgress(currentDate: string): Promise<{
    totalCaloriesConsumed: number;
    totalDays: number;
    averageDailyDeficit: number;
  }> {
    try {
      const allProgressJson = await AsyncStorage.getItem(this.DAILY_PROGRESS_KEY);
      if (!allProgressJson) {
        return { totalCaloriesConsumed: 0, totalDays: 0, averageDailyDeficit: 0 };
      }

      const allProgress: { [date: string]: DailyProgress } = JSON.parse(allProgressJson);

      // Get last 7 days including current date
      const currentDateObj = new Date(currentDate);
      const last7Days: DailyProgress[] = [];

      for (let i = 6; i >= 0; i--) {
        const checkDate = new Date(currentDateObj);
        checkDate.setDate(currentDateObj.getDate() - i);
        const dateStr = checkDate.toISOString().split('T')[0];

        if (allProgress[dateStr]) {
          last7Days.push(allProgress[dateStr]);
        }
      }

      const totalCaloriesConsumed = last7Days.reduce((sum, day) => sum + day.caloriesConsumed, 0);
      const totalDays = last7Days.length;

      // Calculate average daily deficit (this will be used for realistic weight calculation)
      const weightGoal = await this.getWeightGoal();
      const averageDailyDeficit = weightGoal
        ? (weightGoal.dailyCalorieDeficit * totalDays) - totalCaloriesConsumed
        : 0;

      return { totalCaloriesConsumed, totalDays, averageDailyDeficit };
    } catch (error) {
      console.error('❌ Error getting weekly accumulated progress:', error);
      return { totalCaloriesConsumed: 0, totalDays: 0, averageDailyDeficit: 0 };
    }
  }

  // Calculate realistic weight change based on accumulated deficit and time
  private calculateRealisticWeightChange(
    weeklyProgress: { totalCaloriesConsumed: number; totalDays: number; averageDailyDeficit: number },
    weightGoal: WeightGoal,
    todayCalories: number
  ): number {
    // Show progress even from day 1 to encourage users
    if (weeklyProgress.totalDays < 1) {
      return 0;
    }

    // Calculate accumulated deficit over the tracking period
    const accumulatedDeficit = weeklyProgress.averageDailyDeficit;

    // Lower threshold for showing progress - even small deficits count
    if (accumulatedDeficit < 200) { // Reduced from 1000 to 200 calories
      return 0;
    }

    // Calculate realistic weight loss based on accumulated deficit
    // But cap it at maximum healthy rate (1kg per week = 0.14kg per day)
    const theoreticalWeightLoss = accumulatedDeficit / this.CALORIES_PER_KG;
    const maxHealthyWeightLoss = (weeklyProgress.totalDays / 7) * 1.0; // 1kg per week max

    // Return the smaller of theoretical or healthy maximum
    const realisticWeightLoss = Math.min(theoreticalWeightLoss, maxHealthyWeightLoss);

    // Lower minimum threshold to show even small progress
    return realisticWeightLoss > 0.01 ? -realisticWeightLoss : 0; // Minimum 10g to show (reduced from 50g)
  }

  // Calculate weekly trend from recent progress
  private calculateWeeklyTrend(recentProgress: DailyProgress[]): 'improving' | 'stable' | 'declining' {
    if (recentProgress.length < 3) return 'stable';

    const recentWeightChanges = recentProgress
      .map(day => Math.abs(Math.min(0, day.estimatedWeightChange)))
      .filter(change => change > 0);

    if (recentWeightChanges.length < 2) return 'stable';

    const firstHalf = recentWeightChanges.slice(0, Math.floor(recentWeightChanges.length / 2));
    const secondHalf = recentWeightChanges.slice(Math.floor(recentWeightChanges.length / 2));

    const firstHalfAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;

    const improvementThreshold = 0.05; // 50g difference threshold

    if (secondHalfAvg > firstHalfAvg + improvementThreshold) {
      return 'improving';
    } else if (secondHalfAvg < firstHalfAvg - improvementThreshold) {
      return 'declining';
    } else {
      return 'stable';
    }
  }

  // Validate and repair AsyncStorage data integrity with timeout
  async validateAndRepairData(): Promise<boolean> {
    try {
      console.log('🔍 Validating AsyncStorage data integrity...');
      let repairsMade = false;

      // Add overall timeout for validation process
      const timeoutPromise = new Promise<boolean>((_, reject) =>
        setTimeout(() => reject(new Error('Validation timeout')), 3000)
      );

      const validationPromise = async (): Promise<boolean> => {
        // Check weight goal data with timeout
        try {
          const weightGoalJson = await AsyncStorage.getItem(this.WEIGHT_GOAL_KEY);
          if (weightGoalJson) {
            try {
              const weightGoal = JSON.parse(weightGoalJson);
              if (!this.isValidWeightGoal(weightGoal)) {
                console.warn('⚠️ Corrupted weight goal data detected, removing...');
                await AsyncStorage.removeItem(this.WEIGHT_GOAL_KEY);
                repairsMade = true;
              }
            } catch (parseError) {
              console.warn('⚠️ Unparseable weight goal data, removing...');
              await AsyncStorage.removeItem(this.WEIGHT_GOAL_KEY);
              repairsMade = true;
            }
          }
        } catch (error) {
          console.error('❌ Error checking weight goal data (non-blocking):', error);
        }

      // Check daily progress data
      const progressJson = await AsyncStorage.getItem(this.DAILY_PROGRESS_KEY);
      if (progressJson) {
        try {
          const progressData = JSON.parse(progressJson);
          if (!this.isValidProgressData(progressData)) {
            console.warn('⚠️ Corrupted progress data detected, cleaning...');

            // Try to salvage valid entries
            const cleanedData: { [date: string]: DailyProgress } = {};
            if (typeof progressData === 'object') {
              Object.entries(progressData).forEach(([date, progress]) => {
                if (this.isValidDateString(date) && this.isValidDailyProgress(progress)) {
                  cleanedData[date] = progress as DailyProgress;
                }
              });
            }

            if (Object.keys(cleanedData).length > 0) {
              await AsyncStorage.setItem(this.DAILY_PROGRESS_KEY, JSON.stringify(cleanedData));
              console.log(`🔄 Salvaged ${Object.keys(cleanedData).length} valid progress entries`);
            } else {
              await AsyncStorage.removeItem(this.DAILY_PROGRESS_KEY);
              console.log('🗑️ No valid progress data found, cleared storage');
            }
            repairsMade = true;
          }
        } catch (parseError) {
          console.warn('⚠️ Unparseable progress data, removing...');
          await AsyncStorage.removeItem(this.DAILY_PROGRESS_KEY);
          repairsMade = true;
        }
      }

      if (repairsMade) {
        console.log('🔧 Data integrity repairs completed');
      } else {
        console.log('✅ Data integrity validation passed');
      }

      return repairsMade;
    } catch (error) {
      console.error('❌ Error during data validation and repair:', error);
      return false;
    }
  }

  // Clear all data (for testing or reset)
  async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([this.WEIGHT_GOAL_KEY, this.DAILY_PROGRESS_KEY]);
      console.log('✅ All weight tracking data cleared');
    } catch (error) {
      console.error('❌ Error clearing weight tracking data:', error);
      throw error;
    }
  }

  // Debug method to troubleshoot weight progress issues
  async debugWeightProgress(): Promise<void> {
    try {
      console.log('🔍 === WEIGHT PROGRESS DEBUG ===');

      // Check weight goal
      const weightGoal = await this.getWeightGoal();
      console.log('🎯 Weight Goal:', weightGoal);

      // Check daily progress
      const today = new Date().toISOString().split('T')[0];
      const dailyProgress = await this.getDailyProgress(today);
      console.log('📊 Today\'s Progress:', dailyProgress);

      // Check weekly progress
      const weeklyProgress = await this.getWeeklyAccumulatedProgress(today);
      console.log('📈 Weekly Progress:', weeklyProgress);

      // Check storage stats
      const stats = await this.getStorageStats();
      console.log('💾 Storage Stats:', stats);

      // Calculate progress
      const progress = await this.calculateWeightProgress();
      console.log('🏆 Calculated Progress:', progress);

      console.log('🔍 === END DEBUG ===');
    } catch (error) {
      console.error('❌ Debug error:', error);
    }
  }

  // Get storage statistics for debugging
  async getStorageStats(): Promise<{
    weightGoalExists: boolean;
    progressEntriesCount: number;
    oldestEntry: string | null;
    newestEntry: string | null;
    totalStorageSize: number;
  }> {
    try {
      const weightGoalJson = await AsyncStorage.getItem(this.WEIGHT_GOAL_KEY);
      const progressJson = await AsyncStorage.getItem(this.DAILY_PROGRESS_KEY);

      let progressEntriesCount = 0;
      let oldestEntry: string | null = null;
      let newestEntry: string | null = null;

      if (progressJson) {
        try {
          const progressData = JSON.parse(progressJson);
          const dates = Object.keys(progressData).sort();
          progressEntriesCount = dates.length;
          oldestEntry = dates[0] || null;
          newestEntry = dates[dates.length - 1] || null;
        } catch (parseError) {
          console.warn('⚠️ Could not parse progress data for stats');
        }
      }

      const totalStorageSize = (weightGoalJson?.length || 0) + (progressJson?.length || 0);

      return {
        weightGoalExists: !!weightGoalJson,
        progressEntriesCount,
        oldestEntry,
        newestEntry,
        totalStorageSize,
      };
    } catch (error) {
      console.error('❌ Error getting storage stats:', error);
      return {
        weightGoalExists: false,
        progressEntriesCount: 0,
        oldestEntry: null,
        newestEntry: null,
        totalStorageSize: 0,
      };
    }
  }

  // Get today's nutrition data for ProfileContext sync
  async getTodaysNutrition(date?: string): Promise<{
    calories: number;
    protein: number;
    mealsLogged: number;
  }> {
    try {
      const today = date || new Date().toISOString().split('T')[0];
      const progressData = await this.getDailyProgress(today);

      if (progressData) {
        return {
          calories: progressData.caloriesConsumed,
          protein: progressData.proteinConsumed,
          mealsLogged: progressData.mealsLogged
        };
      }

      return { calories: 0, protein: 0, mealsLogged: 0 };
    } catch (error) {
      console.error('❌ Error getting today\'s nutrition:', error);
      return { calories: 0, protein: 0, mealsLogged: 0 };
    }
  }
}

export default WeightGoalTracker.getInstance();
