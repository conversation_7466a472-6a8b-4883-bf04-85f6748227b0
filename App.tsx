import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { View, Text, Platform } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as ImagePicker from 'expo-image-picker';
import { Audio } from 'expo-av';
import * as Location from 'expo-location';

// Import screens - using modern enhanced versions
import HomeScreen from './src/screens/HomeScreenModern';
import ScannerScreen from './src/screens/ScannerScreenModern';
import RecipesScreen from './src/screens/RecipesScreenModern';
import PlanScreen from './src/screens/PlanScreenModern';
import ProfileScreen from './src/screens/ProfileScreenModern';
import AskScreen from './src/screens/AskScreenModern';
import RecipeDetailScreen from './src/screens/RecipeDetailScreenModern';
import CookingTimerScreen from './src/screens/CookingTimerScreenModern';

// Import contexts
import { ProfileProvider, useProfile } from './src/contexts/ProfileContext';
import { OnboardingProvider } from './src/contexts/OnboardingContext';
import { AppStateProvider, useAppState } from './src/contexts/AppStateContext';
import ErrorBoundary from './src/components/ErrorBoundary';

// Import onboarding
import OnboardingNavigator from './src/screens/onboarding/OnboardingNavigator';

// Import navigation components
import AnimatedTabBar from './src/components/navigation/AnimatedTabBar';
import SplashScreen from './src/components/SplashScreen';
import PermissionsScreen from './src/components/PermissionsScreen';

// Import services
import NotificationService from './src/services/NotificationService';
import RecipeCacheService from './src/services/RecipeCacheService';

// Icons are imported in individual components as needed

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

function TabNavigator() {
  return (
    <Tab.Navigator
      tabBar={(props) => <AnimatedTabBar {...props} />}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Recipes" component={RecipesScreen} />
      <Tab.Screen name="Scanner" component={ScannerScreen} />
      <Tab.Screen name="Plan" component={PlanScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
}

function AppContent() {
  const { isOnboardingComplete, completeOnboardingFlow } = useAppState();
  const { isProfileLoaded } = useProfile();

  const [showSplash, setShowSplash] = useState(true);
  const [showPermissions, setShowPermissions] = useState(false);
  const [permissionsChecked, setPermissionsChecked] = useState(false);
  const [navigationState, setNavigationState] = useState();
  const [isReady, setIsReady] = useState(false);
  const [forceMainApp, setForceMainApp] = useState(false);

  // Initialize navigation state and app state handling
  useEffect(() => {
    const restoreState = async () => {
      try {
        const savedStateString = await AsyncStorage.getItem('NAVIGATION_STATE_V1');
        const state = savedStateString ? JSON.parse(savedStateString) : undefined;

        if (state !== undefined) {
          setNavigationState(state);
        }
      } catch (e) {
        console.warn('Failed to restore navigation state:', e);
      } finally {
        setIsReady(true);
      }
    };

    if (!isReady) {
      restoreState();
    }
  }, [isReady]);

  // Timeout management for onboarding status check
  useEffect(() => {
    if (isOnboardingComplete === null && !forceMainApp) {
      const timeout = setTimeout(() => {
        console.log('⚠️ Onboarding status check timed out, forcing main app');
        setForceMainApp(true);
      }, 3000); // 3 second timeout

      return () => clearTimeout(timeout);
    }
  }, [isOnboardingComplete, forceMainApp]);

  // Timeout management for profile loading after onboarding
  useEffect(() => {
    if (!isProfileLoaded && !forceMainApp && isOnboardingComplete === true) {
      const timeout = setTimeout(() => {
        console.log('⚠️ Profile loading timed out after onboarding, proceeding to main app');
        setForceMainApp(true);
      }, 5000); // 5 second timeout for profile loading

      return () => clearTimeout(timeout);
    }
  }, [isProfileLoaded, forceMainApp, isOnboardingComplete]);

  // Disable automatic app state refreshes to prevent loading loops
  // useEffect(() => {
  //   const removeRefreshCallback = AppStateManager.addRefreshCallback(async () => {
  //     try {
  //       await checkOnboardingStatus();
  //       await refreshProfile();
  //     } catch (error) {
  //       console.error('❌ App.tsx: Error in centralized refresh:', error);
  //     }
  //   });
  //   return () => removeRefreshCallback();
  // }, [checkOnboardingStatus, refreshProfile]);

  // Initialize notification service and recipe cache cleanup (without requesting permissions)
  useEffect(() => {
    const initializeServices = async () => {
      try {


        // Only initialize notification channels, don't request permissions
        await NotificationService.initializeChannels();
        console.log('✅ Notification service initialized (without requesting permissions)');

        // Clean up expired recipes on app start
        await RecipeCacheService.cleanupExpiredRecipes();
        console.log('✅ Recipe cache cleanup completed');
      } catch (error) {
        console.error('❌ Failed to initialize services:', error);
      }
    };

    initializeServices();
  }, []);

  // Check if all critical permissions are granted (without requesting them)
  const checkPermissions = async () => {
    try {
      console.log('🔍 Checking critical permissions...');

      // Check permissions without requesting them
      const cameraStatus = await ImagePicker.getCameraPermissionsAsync();
      const audioStatus = await Audio.getPermissionsAsync();
      const mediaStatus = await ImagePicker.getMediaLibraryPermissionsAsync();
      const locationStatus = await Location.getForegroundPermissionsAsync();

      console.log('📷 Camera:', cameraStatus.status);
      console.log('🎤 Audio:', audioStatus.status);
      console.log('📸 Media:', mediaStatus.status);
      console.log('📍 Location:', locationStatus.status);

      // For now, only require camera and media permissions as critical
      // Other permissions can be requested later through the permissions screen
      const criticalPermissionsGranted =
        cameraStatus.status === 'granted' &&
        mediaStatus.status === 'granted';

      console.log('✅ Critical permissions granted:', criticalPermissionsGranted);

      setPermissionsChecked(true);
      setShowPermissions(!criticalPermissionsGranted);
    } catch (error) {
      console.error('❌ Error checking permissions:', error);
      setPermissionsChecked(true);
      setShowPermissions(true); // Show permissions screen if check fails
    }
  };

  // Show custom splash screen first or while loading navigation state
  if (showSplash || !isReady) {
    return (
      <SplashScreen
        onAnimationComplete={() => {
          setShowSplash(false);
          checkPermissions();
        }}
      />
    );
  }

  // Wait for permissions check to complete
  if (!permissionsChecked) {
    return <View style={{ flex: 1, backgroundColor: '#6B7C5A' }} />;
  }

  // Show permissions screen only if permissions are missing
  if (showPermissions) {
    return (
      <PermissionsScreen
        onPermissionsGranted={() => setShowPermissions(false)}
      />
    );
  }

  // Show loading while checking onboarding status (with proper timeout management)
  if (isOnboardingComplete === null && !forceMainApp) {
    console.log('🔄 App: Showing initial loading screen');
    return (
      <View style={{ flex: 1, backgroundColor: '#6B7C5A', justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: '#fcf4ec', fontSize: 16 }}>Loading...</Text>
      </View>
    );
  }

  // If onboarding is NOT complete and not forced, show onboarding
  if ((isOnboardingComplete === false || isOnboardingComplete === null) && !forceMainApp) {
    console.log('🎯 App: Showing onboarding navigator');
    return (
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <NavigationContainer>
            <OnboardingNavigator />
          </NavigationContainer>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    );
  }

  // CRITICAL: Only wait for profile loading if onboarding IS complete
  // This prevents the app from showing broken state during transition
  if (!isProfileLoaded && !forceMainApp && isOnboardingComplete === true) {
    console.log('⏳ App: Waiting for profile to load after onboarding completion');
    return (
      <View style={{ flex: 1, backgroundColor: '#6B7C5A', justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: '#fcf4ec', fontSize: 16 }}>Setting up your profile...</Text>
        <Text style={{ color: '#fcf4ec', fontSize: 12, marginTop: 8 }}>This should only take a moment</Text>
      </View>
    );
  }

  // If we reach here, show the main app
  console.log('🎉 App: Showing main application');
  console.log('📊 App: Final state - onboardingComplete:', isOnboardingComplete, 'profileLoaded:', isProfileLoaded);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <NavigationContainer
          initialState={isOnboardingComplete ? navigationState : undefined}
          onStateChange={(state) => {
            // Only save navigation state if onboarding is complete
            if (isOnboardingComplete) {
              AsyncStorage.setItem('NAVIGATION_STATE_V1', JSON.stringify(state));
            }
          }}
        >
          <Stack.Navigator
                screenOptions={{
                  headerShown: false,
                  presentation: 'card',
                  animationTypeForReplace: 'push',
                  cardStyleInterpolator: ({ current, layouts }) => {
                    return {
                      cardStyle: {
                        transform: [
                          {
                            translateX: current.progress.interpolate({
                              inputRange: [0, 1],
                              outputRange: [layouts.screen.width, 0],
                            }),
                          },
                        ],
                        // Remove opacity interpolation to prevent transparency issues
                        opacity: 1,
                      },
                    };
                  },
                }}
              >
                <Stack.Screen
                  name="MainTabs"
                  component={TabNavigator}
                  options={{
                    headerShown: false
                  }}
                />
            <Stack.Screen
              name="Ask"
              component={AskScreen}
              options={{
                headerShown: false,
                presentation: 'modal',
                cardStyleInterpolator: ({ current, layouts }) => {
                  return {
                    cardStyle: {
                      transform: [
                        {
                          translateY: current.progress.interpolate({
                            inputRange: [0, 1],
                            outputRange: [layouts.screen.height, 0],
                          }),
                        },
                      ],
                    },
                  };
                },
              }}
            />
            <Stack.Screen
              name="RecipeDetail"
              component={RecipeDetailScreen}
              options={{
                headerShown: false,
                presentation: 'card',
              }}
            />
            <Stack.Screen
              name="CookingTimer"
              component={CookingTimerScreen}
              options={{
                headerShown: false,
                presentation: 'card',
              }}
            />
              </Stack.Navigator>
          </NavigationContainer>
          <StatusBar style={Platform.OS === 'ios' ? 'dark' : 'auto'} />
        </SafeAreaProvider>
      </GestureHandlerRootView>
    );
}

export default function App() {
  return (
    <ErrorBoundary onError={(error, errorInfo) => {
      console.error('🚨 App-level error:', error);
      console.error('🚨 Error info:', errorInfo);
    }}>
      <AppStateProvider>
        <ErrorBoundary>
          <ProfileProvider>
            <ErrorBoundary>
              <OnboardingProvider>
                <AppContent />
              </OnboardingProvider>
            </ErrorBoundary>
          </ProfileProvider>
        </ErrorBoundary>
      </AppStateProvider>
    </ErrorBoundary>
  );
}
