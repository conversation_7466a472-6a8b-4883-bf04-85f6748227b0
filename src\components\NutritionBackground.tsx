import React from 'react';
import { View, StyleSheet, ImageBackground } from 'react-native';

interface NutritionBackgroundProps {
  children: React.ReactNode;
  variant?: 'onboarding' | 'main';
}

const NutritionBackground: React.FC<NutritionBackgroundProps> = ({
  children,
  variant = 'main',
}) => {
  return (
    <View style={styles.container}>
      {/* Beautiful Background with New Screens Background Image */}
      <ImageBackground
        source={require('../../assets/screens background.jpg')}
        style={styles.backgroundContainer}
        resizeMode="cover"
      >
        {/* #fcf4ec Overlay for Content Readability */}
        <View style={[
          styles.overlay,
          variant === 'onboarding' && styles.onboardingOverlay
        ]} />
      </ImageBackground>

      {/* Content */}
      <View style={styles.content}>
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#fcf4ec', // Changed from rgba #fcf4ec to solid color
  },
  onboardingOverlay: {
    backgroundColor: '#fcf4ec', // Changed from rgba #fcf4ec to solid color
  },
  content: {
    flex: 1,
    zIndex: 10,
  },
});

export default NutritionBackground;
