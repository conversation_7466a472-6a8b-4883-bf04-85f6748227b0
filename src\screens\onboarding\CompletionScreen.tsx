import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Image, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  ZoomIn,
  BounceIn,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { useOnboarding } from '../../contexts/OnboardingContext';
import { useAppState } from '../../contexts/AppStateContext';
import { useProfile } from '../../contexts/ProfileContext';
import LottieIcon from '../../components/LottieIcon';
import NutritionBackground from '../../components/NutritionBackground';

const CompletionScreen: React.FC = () => {
  const { data, completeOnboarding } = useOnboarding();
  const { completeOnboardingFlow } = useAppState();
  const { refreshProfile } = useProfile();

  useEffect(() => {
    // Celebration haptics
    setTimeout(() => {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }, 500);
  }, []);

  const handleGetStarted = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

    try {
      console.log('🚀 Starting onboarding completion...');

      // Step 1: Complete onboarding data and save to AsyncStorage
      console.log('📝 Step 1: Completing onboarding data...');
      await completeOnboarding();
      console.log('✅ Step 1: Onboarding data completed and saved');

      // Step 2: Wait for profile data to be properly saved and loaded
      console.log('⏱️ Step 2: Waiting for profile data synchronization...');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Step 3: Force refresh profile data to ensure it's loaded
      try {
        console.log('🔄 Step 3: Force refreshing profile data...');
        await refreshProfile();
        console.log('✅ Step 3: Profile data refreshed successfully');
      } catch (profileError) {
        console.error('⚠️ Step 3: Profile refresh failed:', profileError);
        // Don't continue if profile refresh fails - this is critical
        throw new Error('Profile refresh failed during onboarding completion');
      }

      // Step 4: Verify all data is properly saved
      console.log('🔍 Step 4: Verifying data integrity...');
      const onboardingCheck = await AsyncStorage.getItem('onboardingComplete');
      const profileCheck = await AsyncStorage.getItem('userProfile');

      if (onboardingCheck !== 'true') {
        throw new Error('Onboarding completion not properly saved');
      }

      if (!profileCheck) {
        throw new Error('Profile data not properly saved');
      }

      console.log('✅ Step 4: Data integrity verified');

      // Step 5: Complete the onboarding flow to switch to main app
      console.log('🔄 Step 5: Completing onboarding flow transition...');
      await completeOnboardingFlow();
      console.log('✅ Step 5: Onboarding flow completed - ready for main app');

      // Step 6: Final state propagation delay
      console.log('⏱️ Step 6: Final state propagation...');
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('✅ Step 6: State propagation completed');

      console.log('🎉 Onboarding completion process finished successfully!');

    } catch (error) {
      console.error('❌ Critical error during onboarding completion:', error);

      // Show user-friendly error message
      Alert.alert(
        'Setup Error',
        'There was an issue completing your setup. Please try again.',
        [
          {
            text: 'Retry',
            onPress: () => handleGetStarted(),
            style: 'default'
          },
          {
            text: 'Skip for Now',
            onPress: () => {
              console.log('🔄 User chose to skip - forcing completion');
              completeOnboardingFlow();
            },
            style: 'cancel'
          }
        ]
      );
    }
  };

  const getPersonalizedMessage = () => {
    const firstName = data.name.split(' ')[0];
    const goals = data.fitnessObjectives.length;
    const preferences = data.dietaryPreferences.length;
    
    return `Welcome ${firstName}! We've personalized your experience with ${goals} fitness goal${goals !== 1 ? 's' : ''} and ${preferences} dietary preference${preferences !== 1 ? 's' : ''}.`;
  };

  return (
    <NutritionBackground variant="onboarding">
      <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent}>
        {/* Success Animation */}
        <Animated.View entering={ZoomIn.delay(200).duration(800)} style={styles.iconContainer}>
          <Image
            source={require('../../../assets/image final.png')}
            style={styles.logoImage}
            resizeMode="contain"
          />
        </Animated.View>

        {/* Title */}
        <Animated.Text entering={FadeInUp.delay(600).duration(600)} style={styles.title}>
          You're All Set!
        </Animated.Text>

      {/* Personalized Message */}
      <Animated.Text entering={FadeInUp.delay(800).duration(600)} style={styles.message}>
        {getPersonalizedMessage()}
      </Animated.Text>

      {/* Features Preview */}
      <Animated.View entering={FadeInUp.delay(1000).duration(600)} style={styles.featuresContainer}>
        <View style={styles.featureItem}>
          <Ionicons name="checkmark-circle" size={24} color="#6B7C5A" />
          <Text style={styles.featureText}>Personalized meal recommendations</Text>
        </View>
        <View style={styles.featureItem}>
          <Ionicons name="checkmark-circle" size={24} color="#6B7C5A" />
          <Text style={styles.featureText}>AI-powered nutrition tracking</Text>
        </View>
        <View style={styles.featureItem}>
          <Ionicons name="checkmark-circle" size={24} color="#6B7C5A" />
          <Text style={styles.featureText}>Custom calorie and macro goals</Text>
        </View>
        <View style={styles.featureItem}>
          <Ionicons name="checkmark-circle" size={24} color="#6B7C5A" />
          <Text style={styles.featureText}>Smart food scanning technology</Text>
        </View>
      </Animated.View>

      {/* Get Started Button */}
      <Animated.View entering={BounceIn.delay(1400).duration(800)} style={styles.buttonContainer}>
        <TouchableOpacity style={styles.getStartedButton} onPress={handleGetStarted}>
          <View style={styles.buttonContent}>
            <Text style={styles.buttonText}>Start Your Journey</Text>
            <Ionicons name="arrow-forward" size={24} color="#fcf4ec" />
          </View>
        </TouchableOpacity>
      </Animated.View>

      {/* Footer */}
      <Animated.Text entering={FadeInDown.delay(1600).duration(600)} style={styles.footer}>
        Your data is securely stored and ready to personalize your experience
      </Animated.Text>
    </ScrollView>
    </NutritionBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 80,
    paddingBottom: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 32,
    backgroundColor: '#F9FAFB',
    borderRadius: 80,
    padding: 20,
    borderWidth: 3,
    borderColor: '#6B7C5A',
  },
  logoImage: {
    width: 160,
    height: 64,
  },
  title: {
    fontSize: 36,
    fontWeight: '900',
    color: '#6B7C5A',
    textAlign: 'center',
    marginBottom: 24,
    letterSpacing: -0.5,
  },
  message: {
    fontSize: 18,
    fontWeight: '500',
    color: '#8B9A7A',
    textAlign: 'center',
    lineHeight: 26,
    marginBottom: 40,
    paddingHorizontal: 16,
  },
  featuresContainer: {
    width: '100%',
    marginBottom: 48,
    backgroundColor: '#F9FAFB',
    borderRadius: 24,
    padding: 24,
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  featureText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginLeft: 12,
    letterSpacing: 0.3,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 24,
  },
  getStartedButton: {
    borderRadius: 32,
    overflow: 'hidden',
    backgroundColor: '#6B7C5A',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 48,
    gap: 12,
  },
  buttonText: {
    fontSize: 20,
    fontWeight: '800',
    color: '#fcf4ec',
    letterSpacing: 0.5,
  },
  footer: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8B9A7A',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default CompletionScreen;
